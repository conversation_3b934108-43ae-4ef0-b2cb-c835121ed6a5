import React from 'react';
import { Link } from 'react-router-dom';
import { useSupabase } from '../../providers/SupabaseProvider';
import { useUser } from '@clerk/clerk-react';
import RegisterAsHost from '../../components/host/RegisterAsHost';

export default function Unauthorized() {
  const { userProfile, isHost, refreshUserProfile } = useSupabase();
  const { user } = useUser();

  const handleDebugRefresh = async () => {
    console.log('Debug: Current user profile:', userProfile);
    console.log('Debug: Current isHost status:', isHost);
    console.log('Debug: Clerk user:', user);
    await refreshUserProfile();
  };

  return (
    <div className="min-h-screen pt-32 px-4 flex flex-col items-center">
      <div className="max-w-md mx-auto text-center">
        <h1 className="text-3xl font-bold mb-4">Host Registration Required</h1>
        <p className="text-gray-600 mb-8">
          You need to register as a host to access this area.
          Please complete the host registration process first.
        </p>

        {/* Debug info for development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-4 bg-gray-100 rounded text-left text-sm">
            <p><strong>Debug Info:</strong></p>
            <p>Email: {userProfile?.email || 'Not found'}</p>
            <p>Role: {userProfile?.role || 'Not found'}</p>
            <p>Is Host: {isHost ? 'Yes' : 'No'}</p>
            <p>Clerk ID: {user?.id || 'Not found'}</p>
            <button
              onClick={handleDebugRefresh}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs"
            >
              Refresh Profile
            </button>
          </div>
        )}

        <div className="flex flex-col space-y-4">
          <RegisterAsHost />
          <Link to="/host/portal" className="btn-secondary mt-4">
            Go to Host Portal
          </Link>
        </div>
      </div>
    </div>
  );
}
